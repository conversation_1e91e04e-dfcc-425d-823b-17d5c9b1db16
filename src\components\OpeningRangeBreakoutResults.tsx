// src/components/OpeningRangeBreakoutResults.tsx

'use client';

import { OpeningRangeBreakoutResult, TradeResult } from '@/utils/openingRangeBreakout';

interface OpeningRangeBreakoutResultsProps {
  results: OpeningRangeBreakoutResult | null;
  stockSymbol: string;
  stockDate: string;
}

/**
 * Component to display opening range breakout strategy results
 */
export default function OpeningRangeBreakoutResults({ 
  results, 
  stockSymbol, 
  stockDate 
}: OpeningRangeBreakoutResultsProps) {
  if (!results) {
    return null;
  }

  const formatCurrency = (value: number) => {
    const sign = value >= 0 ? '+' : '';
    return `${sign}$${value.toFixed(2)}`;
  };

  const formatPrice = (value: number) => {
    return `$${value.toFixed(2)}`;
  };

  const TradeCard = ({ trade, title }: { trade: TradeResult | null; title: string }) => {
    if (!trade) {
      return (
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-3">{title}</h3>
          <div className="text-gray-400 text-center py-8">
            <div className="text-2xl mb-2">❌</div>
            <div>No {title.toLowerCase()} breakout occurred</div>
          </div>
        </div>
      );
    }

    const isProfit = trade.pnl > 0;
    const pnlColor = isProfit ? 'text-green-400' : 'text-red-400';
    const pnlIcon = isProfit ? '📈' : '📉';

    return (
      <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
          {title}
          <span className="ml-2 text-xl">{trade.type === 'long' ? '🚀' : '🔻'}</span>
        </h3>
        
        <div className="space-y-3">
          {/* Entry Details */}
          <div className="bg-gray-900 rounded p-3">
            <h4 className="text-sm font-medium text-gray-300 mb-2">Entry Details</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-gray-400">Time:</span>
                <div className="text-white font-mono">{trade.entryTime}</div>
              </div>
              <div>
                <span className="text-gray-400">Price:</span>
                <div className="text-white font-mono">{formatPrice(trade.entryPrice)}</div>
              </div>
            </div>
          </div>

          {/* Exit Details */}
          <div className="bg-gray-900 rounded p-3">
            <h4 className="text-sm font-medium text-gray-300 mb-2">Exit Details</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-gray-400">Time:</span>
                <div className="text-white font-mono">{trade.exitTime}</div>
              </div>
              <div>
                <span className="text-gray-400">Price:</span>
                <div className="text-white font-mono">{formatPrice(trade.exitPrice)}</div>
              </div>
            </div>
            <div className="mt-2 text-sm">
              <span className="text-gray-400">Reason:</span>
              <div className="text-white">
                {trade.wasStoppedOut ? '🛑 Stopped Out' : '⏰ End of Data'}
              </div>
            </div>
          </div>

          {/* P&L and Performance */}
          <div className="bg-gray-900 rounded p-3">
            <h4 className="text-sm font-medium text-gray-300 mb-2">Performance</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-gray-400">P&L:</span>
                <div className={`font-mono font-bold ${pnlColor} flex items-center`}>
                  <span className="mr-1">{pnlIcon}</span>
                  {formatCurrency(trade.pnl)}
                </div>
              </div>
              <div>
                <span className="text-gray-400">Bars Held:</span>
                <div className="text-white font-mono">{trade.barsHeld}</div>
              </div>
            </div>
          </div>

          {/* Maximum Favorable Excursion */}
          <div className="bg-gray-900 rounded p-3">
            <h4 className="text-sm font-medium text-gray-300 mb-2">Max Favorable Excursion</h4>
            <div className="space-y-2 text-sm">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <span className="text-gray-400">Amount:</span>
                  <div className="text-green-400 font-mono font-bold">
                    +{formatCurrency(trade.maxFavorableExcursion).substring(1)}
                  </div>
                </div>
                <div>
                  <span className="text-gray-400">Price:</span>
                  <div className="text-white font-mono">{formatPrice(trade.maxFavorableExcursionPrice)}</div>
                </div>
              </div>
              <div>
                <span className="text-gray-400">Time:</span>
                <div className="text-white font-mono text-xs">{trade.maxFavorableExcursionTime}</div>
              </div>
            </div>
          </div>

          {/* Stop Loss */}
          <div className="bg-gray-900 rounded p-3">
            <h4 className="text-sm font-medium text-gray-300 mb-2">Stop Loss</h4>
            <div className="text-sm">
              <span className="text-gray-400">Stop Price:</span>
              <div className="text-red-400 font-mono">{formatPrice(trade.stopPrice)}</div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-gray-900 rounded-lg p-6 border border-gray-700">
      <div className="mb-6">
        <h2 className="text-xl font-bold text-white mb-2 flex items-center">
          <span className="mr-2">📊</span>
          Opening Range Breakout Strategy Results
        </h2>
        <div className="text-gray-400 text-sm">
          {stockSymbol.toUpperCase()} • {stockDate} • 5-minute bars
        </div>
      </div>

      {/* Strategy Overview */}
      <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-3">Strategy Overview</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <span className="text-gray-400">First 3 Bars High:</span>
            <div className="text-white font-mono font-bold">{formatPrice(results.firstThreeBarsHigh)}</div>
          </div>
          <div>
            <span className="text-gray-400">First 3 Bars Low:</span>
            <div className="text-white font-mono font-bold">{formatPrice(results.firstThreeBarsLow)}</div>
          </div>
          <div>
            <span className="text-gray-400">Total Bars Analyzed:</span>
            <div className="text-white font-mono">{results.totalBars}</div>
          </div>
        </div>
      </div>

      {/* Trade Results */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TradeCard trade={results.longTrade} title="Long Trade" />
        <TradeCard trade={results.shortTrade} title="Short Trade" />
      </div>

      {/* Summary */}
      {(results.longTrade || results.shortTrade) && (
        <div className="mt-6 bg-gray-800 rounded-lg p-4 border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-3">Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            {results.longTrade && (
              <div>
                <span className="text-gray-400">Long Trade P&L:</span>
                <div className={`font-mono font-bold ${results.longTrade.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {formatCurrency(results.longTrade.pnl)}
                </div>
              </div>
            )}
            {results.shortTrade && (
              <div>
                <span className="text-gray-400">Short Trade P&L:</span>
                <div className={`font-mono font-bold ${results.shortTrade.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {formatCurrency(results.shortTrade.pnl)}
                </div>
              </div>
            )}
          </div>
          {results.longTrade && results.shortTrade && (
            <div className="mt-3 pt-3 border-t border-gray-700">
              <span className="text-gray-400">Combined P&L:</span>
              <div className={`font-mono font-bold text-lg ${(results.longTrade.pnl + results.shortTrade.pnl) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {formatCurrency(results.longTrade.pnl + results.shortTrade.pnl)}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
