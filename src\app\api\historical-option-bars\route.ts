// src/app/api/historical-option-bars/route.ts (Next.js 13+ App Router)

import { NextRequest, NextResponse } from 'next/server';

const API_URL = 'https://data.alpaca.markets/v1beta1/options/bars';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbol = searchParams.get('symbol');
    const start = searchParams.get('start');
    const end = searchParams.get('end');
    const timeframe = searchParams.get('timeframe') || '1Min';

    if (!symbol || !start) {
      return NextResponse.json({ error: 'Missing symbol or start query param' }, { status: 400 });
    }

    const apiKey = process.env.ALPACA_API_KEY!;
    const apiSecret = process.env.ALPACA_API_SECRET!;

    const url = new URL(API_URL);
    url.searchParams.append('symbols', symbol);
    url.searchParams.append('timeframe', timeframe);
    url.searchParams.append('start', start);
    if (end) {
      url.searchParams.append('end', end);
    }
    url.searchParams.append('limit', '10000');
    url.searchParams.append('sort', 'asc');

    const response = await fetch(url.toString(), {
      headers: {
        'APCA-API-KEY-ID': apiKey,
        'APCA-API-SECRET-KEY': apiSecret,
      },
    });

    if (!response.ok) {
      const text = await response.text();
      return NextResponse.json({ error: `Alpaca API error: ${text}` }, { status: response.status });
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json({ error: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 });
  }
}
