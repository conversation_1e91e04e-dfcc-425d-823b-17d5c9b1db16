// src/utils/fetchHistoricalStockBars.ts

export async function fetchHistoricalStockBars(symbol: string, startDate: string, endDate: string, timeframe: string) {
  const start = `${startDate}T09:30:00-04:00`;
  const end = `${endDate}T16:00:00-04:00`;
  const params = new URLSearchParams({ symbol, start, end, timeframe });

  const res = await fetch(`/api/historical-stock-bars?${params.toString()}`);

  if (!res.ok) {
    const error = await res.json();
    throw new Error(error.error || 'Failed to fetch historical stock bars');
  }

  return await res.json();
}
