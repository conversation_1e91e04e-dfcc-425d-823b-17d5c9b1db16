// src/components/TradingViewChart.tsx

'use client';

import { useEffect, useRef, forwardRef, useImperativeHandle, useState } from 'react';
import { createChart, IChartApi, ISeriesApi, CandlestickData, Time, IPriceLine, MouseEventParams } from 'lightweight-charts';

export interface TradingViewChartRef {
  updateData: (data: CandlestickData<Time>[]) => void;
  clearData: () => void;
  resize: () => void;
  addPriceLines: (highPrice: number, lowPrice: number) => void;
  clearPriceLines: () => void;
}

interface TradingViewChartProps {
  className?: string;
}

interface HoverData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
}

/**
 * TradingViewChart Component
 *
 * A professional TradingView chart component that displays candlestick data.
 * Uses the lightweight-charts library for optimal performance.
 *
 * Features:
 * - Responsive design that fills the container
 * - Real candlestick chart display
 * - Methods to update and clear chart data
 * - Dark theme with professional styling
 * - Real-time data updates
 */
const TradingViewChart = forwardRef<TradingViewChartRef, TradingViewChartProps>(
  ({ className = '' }, ref) => {
    const chartContainerRef = useRef<HTMLDivElement>(null);
    const chartRef = useRef<IChartApi | null>(null);
    const seriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null);
    const priceLinesRef = useRef<IPriceLine[]>([]);
    const [isClient, setIsClient] = useState(false);
    const [hoverData, setHoverData] = useState<HoverData | null>(null);

    useImperativeHandle(ref, () => ({
      updateData: (data: CandlestickData<Time>[]) => {
        if (seriesRef.current && data.length > 0) {
          // Data is already in the correct format from the transformation utility
          seriesRef.current.setData(data);
        }
      },
      clearData: () => {
        if (seriesRef.current) {
          seriesRef.current.setData([]);
        }
        // Also clear price lines when clearing data
        if (seriesRef.current && priceLinesRef.current.length > 0) {
          priceLinesRef.current.forEach(priceLine => {
            seriesRef.current!.removePriceLine(priceLine);
          });
          priceLinesRef.current = [];
        }
      },
      resize: () => {
        if (chartRef.current && chartContainerRef.current) {
          chartRef.current.applyOptions({
            width: chartContainerRef.current.clientWidth,
            height: chartContainerRef.current.clientHeight,
          });
        }
      },
      addPriceLines: (highPrice: number, lowPrice: number) => {
        if (seriesRef.current) {
          // Clear existing price lines first
          if (priceLinesRef.current.length > 0) {
            priceLinesRef.current.forEach(priceLine => {
              seriesRef.current!.removePriceLine(priceLine);
            });
            priceLinesRef.current = [];
          }

          // Create high price line
          const highPriceLine = seriesRef.current.createPriceLine({
            price: highPrice,
            color: 'white',
            lineWidth: 1,
            lineStyle: 0, // Solid line
            axisLabelVisible: true,
            title: 'High (First 3 Bars)',
          });

          // Create low price line
          const lowPriceLine = seriesRef.current.createPriceLine({
            price: lowPrice,
            color: 'white',
            lineWidth: 1,
            lineStyle: 0, // Solid line
            axisLabelVisible: true,
            title: 'Low (First 3 Bars)',
          });

          // Store references to the price lines
          priceLinesRef.current = [highPriceLine, lowPriceLine];
        }
      },
      clearPriceLines: () => {
        if (seriesRef.current && priceLinesRef.current.length > 0) {
          priceLinesRef.current.forEach(priceLine => {
            seriesRef.current!.removePriceLine(priceLine);
          });
          priceLinesRef.current = [];
        }
      }
    }));

    // Set client-side flag to avoid hydration mismatch
    useEffect(() => {
      setIsClient(true);
    }, []);

    useEffect(() => {
      if (!chartContainerRef.current || !isClient) {
        return;
      }

      // Create the chart with mobile-friendly settings
      const isMobile = window.innerWidth < 768;
      const chart = createChart(chartContainerRef.current, {
        width: chartContainerRef.current.clientWidth,
        height: chartContainerRef.current.clientHeight,
        layout: {
          background: { color: '#1a1a1a' },
          textColor: '#d1d4dc',
          fontSize: isMobile ? 10 : 12, // Smaller font on mobile
        },
        grid: {
          vertLines: { color: '#2B2B43' },
          horzLines: { color: '#2B2B43' },
        },
        crosshair: {
          mode: 0, // 0 = Normal mode (no snapping), 1 = Magnet mode (snaps to data points)
        },
        localization: {
          timeFormatter: (time: number) => {
            // Convert Unix timestamp to EST for crosshair tooltip
            const date = new Date(time * 1000);
            return date.toLocaleString('en-US', {
              timeZone: 'America/New_York', // EST/EDT timezone
              month: '2-digit',
              day: '2-digit',
              year: 'numeric',
              hour: '2-digit',
              minute: '2-digit',
              hour12: true
            });
          },
        },
        rightPriceScale: {
          borderColor: '#485c7b',
        },
        timeScale: {
          borderColor: '#485c7b',
          timeVisible: true,
          secondsVisible: false,
          // Configure timezone display for EST with mobile-friendly formatting
          tickMarkFormatter: (time: number) => {
            // Convert Unix timestamp back to Date and format as EST
            const date = new Date(time * 1000);
            const isMobile = isClient && window.innerWidth < 768;
            return date.toLocaleTimeString('en-US', {
              timeZone: 'America/New_York', // EST/EDT timezone
              hour: isMobile ? 'numeric' : '2-digit',
              minute: '2-digit',
              hour12: true
            });
          },
        },
      });

      // Create candlestick series
      const candlestickSeries = chart.addCandlestickSeries({
        upColor: '#4bffb5',
        downColor: '#ff4976',
        borderDownColor: '#ff4976',
        borderUpColor: '#4bffb5',
        wickDownColor: '#ff4976',
        wickUpColor: '#4bffb5',
      });

      chartRef.current = chart;
      seriesRef.current = candlestickSeries;

      // Add crosshair move event listener to show OHLC data on hover
      chart.subscribeCrosshairMove((param: MouseEventParams) => {
        if (param.time && param.seriesData && param.seriesData.has(candlestickSeries)) {
          const data = param.seriesData.get(candlestickSeries) as CandlestickData<Time>;
          if (data) {
            // Format time for display
            const timeValue = typeof param.time === 'number' ? param.time : (param.time as any).timestamp || param.time;
            const date = new Date(timeValue * 1000);
            const timeString = date.toLocaleString('en-US', {
              timeZone: 'America/New_York',
              month: '2-digit',
              day: '2-digit',
              year: 'numeric',
              hour: '2-digit',
              minute: '2-digit',
              hour12: true
            });

            setHoverData({
              time: timeString,
              open: data.open,
              high: data.high,
              low: data.low,
              close: data.close,
            });
          }
        } else {
          // Clear hover data when not hovering over a candlestick
          setHoverData(null);
        }
      });

      // Handle resize
      const handleResize = () => {
        if (chartContainerRef.current && chartRef.current) {
          chartRef.current.applyOptions({
            width: chartContainerRef.current.clientWidth,
            height: chartContainerRef.current.clientHeight,
          });
        }
      };

      window.addEventListener('resize', handleResize);

      // Cleanup
      return () => {
        window.removeEventListener('resize', handleResize);
        if (chartRef.current) {
          chartRef.current.remove();
        }
      };
    }, [isClient]);

    return (
      <div
        ref={chartContainerRef}
        className={`w-full h-full bg-gray-900 min-h-[200px] relative ${className}`}
      >
        {!isClient && (
          <div className="w-full h-full flex items-center justify-center text-white">
            <div className="text-center">
              <div className="text-2xl mb-2">📈</div>
              <div className="text-sm text-gray-400">Loading chart...</div>
            </div>
          </div>
        )}

        {/* OHLC Data Display on Hover */}
        {hoverData && (
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-10 pointer-events-none">
            <div className="bg-black bg-opacity-80 text-white px-4 py-2 rounded-lg shadow-lg border border-gray-600">
              <div className="text-xs text-white mb-1 text-center">{hoverData.time}</div>
              <div className="flex space-x-4 text-sm font-mono text-white">
                <span>O: {hoverData.open.toFixed(2)}</span>
                <span>H: {hoverData.high.toFixed(2)}</span>
                <span>L: {hoverData.low.toFixed(2)}</span>
                <span>C: {hoverData.close.toFixed(2)}</span>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }
);

TradingViewChart.displayName = 'TradingViewChart';

export default TradingViewChart;
