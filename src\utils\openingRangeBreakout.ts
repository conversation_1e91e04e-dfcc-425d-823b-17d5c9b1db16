// src/utils/openingRangeBreakout.ts

import { ChartDataPoint } from './transformChartData';

export interface TradeResult {
  type: 'long' | 'short';
  entryBarIndex: number;
  entryTime: string;
  entryPrice: number;
  stopPrice: number;
  exitBarIndex: number;
  exitTime: string;
  exitPrice: number;
  pnl: number;
  maxFavorableExcursion: number;
  maxFavorableExcursionPrice: number;
  maxFavorableExcursionTime: string;
  barsHeld: number;
  wasStoppedOut: boolean;
}

export interface OpeningRangeBreakoutResult {
  firstThreeBarsHigh: number;
  firstThreeBarsLow: number;
  longTrade: TradeResult | null;
  shortTrade: TradeResult | null;
  totalBars: number;
}

/**
 * Analyzes chart data for opening range breakout strategy
 * @param chartData - Array of chart data points (5-minute bars)
 * @returns Analysis results with trade simulations
 */
export function analyzeOpeningRangeBreakout(chartData: ChartDataPoint[]): OpeningRangeBreakoutResult {
  if (chartData.length < 4) {
    return {
      firstThreeBarsHigh: 0,
      firstThreeBarsLow: 0,
      longTrade: null,
      shortTrade: null,
      totalBars: chartData.length,
    };
  }

  // Calculate first 3 bars high and low
  const firstThreeBars = chartData.slice(0, 3);
  const firstThreeBarsHigh = Math.max(...firstThreeBars.map(bar => bar.high));
  const firstThreeBarsLow = Math.min(...firstThreeBars.map(bar => bar.low));

  let longTrade: TradeResult | null = null;
  let shortTrade: TradeResult | null = null;

  // Look for breakouts starting from the 4th bar (index 3)
  for (let i = 3; i < chartData.length; i++) {
    const currentBar = chartData[i];

    // Check for long breakout (close above first 3 bars high)
    if (!longTrade && currentBar.close > firstThreeBarsHigh) {
      longTrade = simulateLongTrade(chartData, i, firstThreeBarsHigh, firstThreeBarsLow);
    }

    // Check for short breakout (close below first 3 bars low)
    if (!shortTrade && currentBar.close < firstThreeBarsLow) {
      shortTrade = simulateShortTrade(chartData, i, firstThreeBarsHigh, firstThreeBarsLow);
    }

    // If both trades found, break early
    if (longTrade && shortTrade) {
      break;
    }
  }

  return {
    firstThreeBarsHigh,
    firstThreeBarsLow,
    longTrade,
    shortTrade,
    totalBars: chartData.length,
  };
}

/**
 * Simulates a long trade entry on the next bar open after breakout
 */
function simulateLongTrade(
  chartData: ChartDataPoint[],
  breakoutBarIndex: number,
  firstThreeBarsHigh: number,
  firstThreeBarsLow: number
): TradeResult | null {
  // Entry is on the next bar open
  const entryBarIndex = breakoutBarIndex + 1;
  if (entryBarIndex >= chartData.length) {
    return null; // No next bar available
  }

  const entryBar = chartData[entryBarIndex];
  const entryPrice = entryBar.open;
  const stopPrice = firstThreeBarsLow;

  // Track the trade through subsequent bars
  let maxFavorableExcursion = 0;
  let maxFavorableExcursionPrice = entryPrice;
  let maxFavorableExcursionTime = formatTime(entryBar.time);
  let exitBarIndex = entryBarIndex;
  let exitPrice = entryPrice;
  let wasStoppedOut = false;

  // Check each bar after entry for stop out or end of data
  for (let i = entryBarIndex; i < chartData.length; i++) {
    const currentBar = chartData[i];
    
    // Check if stopped out (low touches or goes below stop)
    if (currentBar.low <= stopPrice) {
      exitBarIndex = i;
      exitPrice = stopPrice;
      wasStoppedOut = true;
      break;
    }

    // Track maximum favorable excursion
    const currentFavorableExcursion = currentBar.high - entryPrice;
    if (currentFavorableExcursion > maxFavorableExcursion) {
      maxFavorableExcursion = currentFavorableExcursion;
      maxFavorableExcursionPrice = currentBar.high;
      maxFavorableExcursionTime = formatTime(currentBar.time);
    }

    // If this is the last bar and we haven't been stopped out, exit at close
    if (i === chartData.length - 1) {
      exitBarIndex = i;
      exitPrice = currentBar.close;
    }
  }

  const pnl = exitPrice - entryPrice;
  const barsHeld = exitBarIndex - entryBarIndex + 1;

  return {
    type: 'long',
    entryBarIndex,
    entryTime: formatTime(entryBar.time),
    entryPrice,
    stopPrice,
    exitBarIndex,
    exitTime: formatTime(chartData[exitBarIndex].time),
    exitPrice,
    pnl,
    maxFavorableExcursion,
    maxFavorableExcursionPrice,
    maxFavorableExcursionTime,
    barsHeld,
    wasStoppedOut,
  };
}

/**
 * Simulates a short trade entry on the next bar open after breakout
 */
function simulateShortTrade(
  chartData: ChartDataPoint[],
  breakoutBarIndex: number,
  firstThreeBarsHigh: number,
  firstThreeBarsLow: number
): TradeResult | null {
  // Entry is on the next bar open
  const entryBarIndex = breakoutBarIndex + 1;
  if (entryBarIndex >= chartData.length) {
    return null; // No next bar available
  }

  const entryBar = chartData[entryBarIndex];
  const entryPrice = entryBar.open;
  const stopPrice = firstThreeBarsHigh;

  // Track the trade through subsequent bars
  let maxFavorableExcursion = 0;
  let maxFavorableExcursionPrice = entryPrice;
  let maxFavorableExcursionTime = formatTime(entryBar.time);
  let exitBarIndex = entryBarIndex;
  let exitPrice = entryPrice;
  let wasStoppedOut = false;

  // Check each bar after entry for stop out or end of data
  for (let i = entryBarIndex; i < chartData.length; i++) {
    const currentBar = chartData[i];
    
    // Check if stopped out (high touches or goes above stop)
    if (currentBar.high >= stopPrice) {
      exitBarIndex = i;
      exitPrice = stopPrice;
      wasStoppedOut = true;
      break;
    }

    // Track maximum favorable excursion (for short, this is entry price - low)
    const currentFavorableExcursion = entryPrice - currentBar.low;
    if (currentFavorableExcursion > maxFavorableExcursion) {
      maxFavorableExcursion = currentFavorableExcursion;
      maxFavorableExcursionPrice = currentBar.low;
      maxFavorableExcursionTime = formatTime(currentBar.time);
    }

    // If this is the last bar and we haven't been stopped out, exit at close
    if (i === chartData.length - 1) {
      exitBarIndex = i;
      exitPrice = currentBar.close;
    }
  }

  const pnl = entryPrice - exitPrice; // For short trades, profit when exit < entry
  const barsHeld = exitBarIndex - entryBarIndex + 1;

  return {
    type: 'short',
    entryBarIndex,
    entryTime: formatTime(entryBar.time),
    entryPrice,
    stopPrice,
    exitBarIndex,
    exitTime: formatTime(chartData[exitBarIndex].time),
    exitPrice,
    pnl,
    maxFavorableExcursion,
    maxFavorableExcursionPrice,
    maxFavorableExcursionTime,
    barsHeld,
    wasStoppedOut,
  };
}

/**
 * Formats timestamp for display
 */
function formatTime(timestamp: number): string {
  const date = new Date(timestamp * 1000);
  return date.toLocaleString('en-US', {
    timeZone: 'America/New_York',
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
}
