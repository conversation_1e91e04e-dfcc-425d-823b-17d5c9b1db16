// src/app/page.tsx

'use client';

import { useState, useRef, useEffect } from 'react';
import { formatAlpacaOptionSymbol } from '../utils/formatAlpacaOptionSymbol';
import { fetchHistoricalOptionBars } from '@/utils/fetchHistoricalOptionBars';
import { fetchHistoricalStockBars } from '@/utils/fetchHistoricalStockBars';
import { transformAlpacaDataToChart, validateChartData } from '@/utils/transformChartData';
import TradingViewChart, { TradingViewChartRef } from '@/components/TradingViewChart';
import AutosuggestInput from '@/components/AutosuggestInput';
import tickersData from '@/data/tickers.json';

export default function HomePage() {
  // Form toggle state
  const [formType, setFormType] = useState<'options' | 'stocks'>('options');

  // Options form state
  const [symbol, setSymbol] = useState('');
  const [expiration, setExpiration] = useState('');
  const [type, setType] = useState<'C' | 'P'>('C');
  const [strike, setStrike] = useState('');
  const [timeframe, setTimeframe] = useState('1Min');
  const [startDate, setStartDate] = useState(() => {
    // Default to current date
    const today = new Date();
    return today.toISOString().split('T')[0];
  });
  const [endDate, setEndDate] = useState(() => {
    // Default to current date (will be updated when expiration is set)
    const today = new Date();
    return today.toISOString().split('T')[0];
  });
  const [hasUserChangedDateRange, setHasUserChangedDateRange] = useState(false);

  // Stock form state
  const [stockSymbol, setStockSymbol] = useState('');
  const [stockDate, setStockDate] = useState(() => {
    // Default to current date
    const today = new Date();
    return today.toISOString().split('T')[0];
  });

  // Common state
  const [isFormMinimized, setIsFormMinimized] = useState(false);
  const [hasDataBeenFetched, setHasDataBeenFetched] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const chartRef = useRef<TradingViewChartRef>(null);

  const formatted = formatAlpacaOptionSymbol(
    symbol,
    expiration,
    type,
    parseFloat(strike)
  );

  // Auto-update both start and end dates when expiration changes (unless user has manually changed them)
  useEffect(() => {
    if (expiration && !hasUserChangedDateRange) {
      setStartDate(expiration);
      setEndDate(expiration);
    }
  }, [expiration, hasUserChangedDateRange]);

  // Trigger chart resize when form minimize state changes
  useEffect(() => {
    const timer = setTimeout(() => {
      if (chartRef.current) {
        chartRef.current.resize();
      }
    }, 350); // Wait for CSS transition to complete

    return () => clearTimeout(timer);
  }, [isFormMinimized]);

  async function handleFetch() {
    try {
      setIsLoading(true);
      setError(null);

      // Clear existing chart data first
      if (chartRef.current) {
        chartRef.current.clearData();
      }

      let alpacaData;

      if (formType === 'options') {
        alpacaData = await fetchHistoricalOptionBars(formatted, startDate, endDate, timeframe);
      } else {
        // For stocks, use the stock date for both start and end
        alpacaData = await fetchHistoricalStockBars(stockSymbol, stockDate, stockDate, '5Min');
      }

      console.log('Raw Alpaca data:', alpacaData);

      // Transform the Alpaca data into chart-compatible format
      const chartData = transformAlpacaDataToChart(alpacaData);
      console.log('Transformed chart data:', chartData);

      // Validate the data before updating the chart
      if (validateChartData(chartData)) {
        // Update the chart with the new data
        if (chartRef.current) {
          chartRef.current.updateData(chartData);

          // For stock data (5min charts), add price lines for first 3 bars
          if (formType === 'stocks' && chartData.length >= 3) {
            const firstThreeBars = chartData.slice(0, 3);
            const highestHigh = Math.max(...firstThreeBars.map(bar => bar.high));
            const lowestLow = Math.min(...firstThreeBars.map(bar => bar.low));

            console.log('First 3 bars high/low:', { highestHigh, lowestLow, firstThreeBars });

            // Add the price lines
            chartRef.current.addPriceLines(highestHigh, lowestLow);
          }
        }
        setHasDataBeenFetched(true);
      } else {
        throw new Error('Invalid chart data received - no valid data points found');
      }
    } catch (error) {
      console.error('Fetch error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch chart data';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }


  return (
    <main className="h-screen w-full bg-gray-900 flex flex-col lg:flex-row overflow-hidden">
      {/* TradingView Chart - Top on mobile, Left 3/4 on desktop */}
      <div className={`flex-1 ${isFormMinimized ? 'h-full' : 'h-64 sm:h-80 md:h-96'} lg:h-full lg:w-3/4 relative transition-all duration-300 ease-in-out`}>
        <TradingViewChart ref={chartRef} className="w-full h-full" />

        {/* Chart Overlay Messages */}
        {!hasDataBeenFetched && !isLoading && !error && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-90 z-10">
            <div className="text-center text-white p-6">
              <div className="text-4xl mb-4">📊</div>
              <h2 className="text-xl sm:text-2xl font-semibold mb-2">Fill Out Form to Fetch Chart</h2>
              <p className="text-gray-300 text-sm sm:text-base">
                Enter option details in the form and click &quot;Fetch Historical Bars&quot; to view chart data
              </p>
            </div>
          </div>
        )}

        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-90 z-10">
            <div className="text-center text-white p-6">
              <div className="animate-spin text-4xl mb-4">⏳</div>
              <h2 className="text-xl sm:text-2xl font-semibold mb-2">Loading Chart Data...</h2>
              <p className="text-gray-300 text-sm sm:text-base">
                Fetching historical option bars from Alpaca
              </p>
            </div>
          </div>
        )}

        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-90 z-10">
            <div className="text-center text-white p-6 max-w-md mx-auto">
              <div className="text-4xl mb-4">❌</div>
              <h2 className="text-xl sm:text-2xl font-semibold mb-2 text-red-400">Error Loading Chart</h2>
              <p className="text-gray-300 text-sm sm:text-base mb-4">
                {error}
              </p>
              <button
                type="button"
                onClick={() => setError(null)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm"
              >
                Dismiss
              </button>
            </div>
          </div>
        )}

        {/* Mobile Toggle Button - Only visible on mobile when form is minimized */}
        {isFormMinimized && (
          <button
            type="button"
            onClick={() => {
              setIsFormMinimized(false);
              // Trigger resize after a short delay to allow DOM update
              setTimeout(() => {
                if (chartRef.current) {
                  chartRef.current.resize();
                }
              }, 100);
            }}
            className="absolute bottom-4 right-4 lg:hidden bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 hover:scale-110 transition-all duration-200 z-10 animate-in fade-in zoom-in duration-300"
            aria-label="Show form"
            title="Show option form"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
            </svg>
          </button>
        )}
      </div>

      {/* Thin separator border - horizontal on mobile, vertical on desktop */}
      {!isFormMinimized && <div className="h-px lg:h-full lg:w-px bg-gray-600"></div>}

      {/* Alpaca Option Symbol Generator Form - Bottom on mobile, Right 1/4 on desktop */}
      {!isFormMinimized && (
        <div className="w-full lg:w-1/4 h-auto lg:h-full bg-white overflow-y-auto animate-in slide-in-from-bottom duration-300 lg:animate-none">
          <div className="p-4 sm:p-6 space-y-4 sm:space-y-6 relative">
            {/* Mobile Minimize Button - Only visible on mobile */}
            <button
              type="button"
              onClick={() => {
                setIsFormMinimized(true);
                // Trigger resize after a short delay to allow DOM update
                setTimeout(() => {
                  if (chartRef.current) {
                    chartRef.current.resize();
                  }
                }, 100);
              }}
              className="absolute top-2 right-2 lg:hidden bg-gray-200 text-gray-600 p-2 rounded-full hover:bg-gray-300 hover:scale-110 transition-all duration-200 animate-pulse"
              aria-label="Minimize form"
              title="Minimize form to view chart fullscreen"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
            </button>
          <h1 className="text-xl sm:text-2xl font-bold text-center text-gray-800">
            {formType === 'options' ? '🧮 Option Historical Charts' : '📊 5min Historical Charts'}
          </h1>

          {/* Form Toggle Switch */}
          <div className="flex items-center justify-center space-x-4 py-2">
            <span className={`text-sm font-medium ${formType === 'options' ? 'text-blue-600' : 'text-gray-500'}`}>
              Options
            </span>
            <button
              type="button"
              onClick={() => {
                setFormType(formType === 'options' ? 'stocks' : 'options');
                setError(null);
                setHasDataBeenFetched(false);
                if (chartRef.current) {
                  chartRef.current.clearData();
                }
              }}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                formType === 'stocks' ? 'bg-blue-600' : 'bg-gray-200'
              }`}
              aria-label={`Switch to ${formType === 'options' ? 'stocks' : 'options'} form`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  formType === 'stocks' ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`text-sm font-medium ${formType === 'stocks' ? 'text-blue-600' : 'text-gray-500'}`}>
              Stocks
            </span>
          </div>

          <div className="space-y-3 sm:space-y-4">
            {formType === 'options' ? (
              <>
                {/* Symbol */}
                <div>
                  <label htmlFor="symbol" className="block text-sm font-medium text-gray-700 mb-1">
                    Underlying Symbol
                  </label>
                  <AutosuggestInput
                    id="symbol"
                    value={symbol}
                    onChange={setSymbol}
                    suggestions={tickersData}
                    placeholder="e.g. AAPL"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                    maxSuggestions={8}
                  />
                </div>

            {/* Expiration */}
            <div>
              <label htmlFor="expiration" className="block text-sm font-medium text-gray-700 mb-1">
                Expiration Date
              </label>
              <input
                id="expiration"
                type="date"
                value={expiration}
                onChange={(e) => setExpiration(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
              />
            </div>

            {/* Option Type */}
            <div>
              <label htmlFor="option-type" className="block text-sm font-medium text-gray-700 mb-1">
                Option Type
              </label>
              <select
                id="option-type"
                value={type}
                onChange={(e) => setType(e.target.value as 'C' | 'P')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base bg-white"
              >
                <option value="C">Call</option>
                <option value="P">Put</option>
              </select>
            </div>

            {/* Strike Price */}
            <div>
              <label htmlFor="strike" className="block text-sm font-medium text-gray-700 mb-1">
                Strike Price
              </label>
              <input
                id="strike"
                type="number"
                step="0.01"
                value={strike}
                onChange={(e) => setStrike(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                placeholder="e.g. 150.00"
              />
            </div>

            {/* Timeframe */}
            <div>
              <label htmlFor="timeframe" className="block text-sm font-medium text-gray-700 mb-1">
                Timeframe
              </label>
              <select
                id="timeframe"
                value={timeframe}
                onChange={(e) => setTimeframe(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base bg-white"
              >
                <option value="1Min">1 Minute</option>
                <option value="5Min">5 Minutes</option>
                <option value="15Min">15 Minutes</option>
                <option value="30Min">30 Minutes</option>
                <option value="1Hour">1 Hour</option>
                <option value="4Hour">4 Hour</option>
                <option value="1Day">1 Day</option>
                <option value="1Week">1 Week</option>
                <option value="1Month">1 Month</option>
              </select>
            </div>

            {/* Chart Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Chart Date Range
              </label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label htmlFor="start-date" className="block text-xs text-gray-500 mb-1">
                    Start Date
                  </label>
                  <input
                    id="start-date"
                    type="date"
                    value={startDate}
                    onChange={(e) => {
                      setStartDate(e.target.value);
                      // Mark that user has manually changed the date range
                      setHasUserChangedDateRange(true);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                  />
                </div>
                <div>
                  <label htmlFor="end-date" className="block text-xs text-gray-500 mb-1">
                    End Date
                  </label>
                  <input
                    id="end-date"
                    type="date"
                    value={endDate}
                    onChange={(e) => {
                      setEndDate(e.target.value);
                      // Mark that user has manually changed the date range
                      setHasUserChangedDateRange(true);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                  />
                </div>
              </div>
            </div>
              </>
            ) : (
              <>
                {/* Stock Symbol */}
                <div>
                  <label htmlFor="stock-symbol" className="block text-sm font-medium text-gray-700 mb-1">
                    Symbol
                  </label>
                  <AutosuggestInput
                    id="stock-symbol"
                    value={stockSymbol}
                    onChange={setStockSymbol}
                    suggestions={tickersData}
                    placeholder="e.g. SPY"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                    maxSuggestions={8}
                  />
                </div>

                {/* Date */}
                <div>
                  <label htmlFor="stock-date" className="block text-sm font-medium text-gray-700 mb-1">
                    Date
                  </label>
                  <input
                    id="stock-date"
                    type="date"
                    value={stockDate}
                    onChange={(e) => setStockDate(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                  />
                </div>
              </>
            )}
          </div>

          {formType === 'options' && (
            <div className="pt-3 sm:pt-4 border-t border-gray-200">
              <h2 className="text-sm font-semibold text-gray-600 mb-2">Generated Symbol:</h2>
              <code className="block text-sm sm:text-base bg-gray-100 p-2 sm:p-3 rounded-md text-blue-600 font-mono break-all">
                {formatted || '(fill out all fields)'}
              </code>
            </div>
          )}
          <button
            type="button"
            onClick={handleFetch}
            className="w-full px-4 py-3 bg-blue-600 text-white text-base font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={
              formType === 'options'
                ? (!formatted || isLoading)
                : (!stockSymbol || !stockDate || isLoading)
            }
          >
            {isLoading ? (
              <span className="flex items-center justify-center">
                <span className="animate-spin mr-2">⏳</span>
                Loading...
              </span>
            ) : (
              formType === 'options' ? 'Fetch Historical Bars' : 'Fetch 5min Chart'
            )}
          </button>
        </div>
        </div>
      )}
    </main>
  );
}
