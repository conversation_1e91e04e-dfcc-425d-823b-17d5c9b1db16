// src/utils/formatAlpacaOptionSymbol.ts

export function formatAlpacaOptionSymbol(
  symbol: string,
  expiration: string,
  type: 'C' | 'P',
  strike: number
): string {
  if (!symbol || !expiration || !type || isNaN(strike)) return '';

  const [year, month, day] = expiration.split('-');
  const yy = year?.slice(-2) ?? '';
  const mm = month?.padStart(2, '0') ?? '';
  const dd = day?.padStart(2, '0') ?? '';

  const s = type.toUpperCase();
  if (s !== 'C' && s !== 'P') return '';

  const strikeInt = Math.round(strike * 1000);
  const strikeStr = strikeInt.toString().padStart(8, '0');

  return `${symbol.toUpperCase()}${yy}${mm}${dd}${s}${strikeStr}`;
}
