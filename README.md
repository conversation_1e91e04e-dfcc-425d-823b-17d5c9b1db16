# 📈 Option Historical Charts

A professional web application for visualizing historical options data with interactive TradingView-style charts. Built with Next.js, TypeScript, and the Alpaca Markets API.

## 🚀 Features

- **Real-time Options Data**: Fetch historical options bars from Alpaca Markets API
- **Multiple Timeframes**: Support for 1, 5, 15, and 30-minute candlestick intervals
- **Professional Charts**: Interactive candlestick charts powered by lightweight-charts library
- **Mobile-Responsive**: Optimized for both desktop and mobile devices with collapsible form sections
- **Symbol Formatting**: Automatic Alpaca-compatible option symbol generation
- **Real-time Updates**: Live chart updates with proper data validation
- **Dark Theme**: Professional dark theme with customizable styling

## 🛠️ Tech Stack

- **Frontend**: Next.js 15.3.5 with React 19
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4
- **Charts**: Lightweight Charts 4.2.3
- **API**: Alpaca Markets Options Data API
- **Development**: ESLint, Turbopack for fast development

## 📋 Prerequisites

Before running this application, you need:

1. **Node.js** (version 16.9 or higher)
2. **npm** (version 6 or higher)
3. **Alpaca Markets Account** with API access
4. **Alpaca API Credentials** (API Key and Secret)

## ⚙️ Environment Setup

1. Create a `.env.local` file in the root directory:

```bash
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_API_SECRET=your_alpaca_secret_key_here
```

2. Replace the placeholder values with your actual Alpaca Markets API credentials.

⚠️ **SECURITY WARNING**: Never use `NEXT_PUBLIC_` prefix for API keys or secrets as they will be exposed to frontend users. The variables above are server-side only and secure.

## 🚀 Getting Started

1. **Install dependencies:**

```bash
npm install
```

2. **Run the development server:**

```bash
npm run dev
```

3. **Open your browser:**

Navigate to [http://localhost:3000](http://localhost:3000) to see the application.

## 📱 Mobile Development

The application is configured for mobile testing with network access. The Next.js config allows connections from local network IPs for testing on mobile devices.

## 🎯 How to Use

1. **Enter Stock Symbol**: Input the underlying stock symbol (e.g., "AAPL", "TSLA")
2. **Set Expiration Date**: Choose the option expiration date (YYYY-MM-DD format)
3. **Select Option Type**: Choose Call (C) or Put (P)
4. **Enter Strike Price**: Input the strike price
5. **Select Timeframe**: Choose the candlestick timeframe (1, 5, 15, or 30 minutes)
6. **Fetch Data**: Click "Fetch Historical Bars" to load and display the chart

The application will automatically format the option symbol according to Alpaca's requirements and fetch historical bars with your selected timeframe starting from 9:30 AM EST on the expiration date.

## 📊 Chart Features

- **Interactive Candlestick Charts**: Professional OHLC visualization
- **Mobile-Optimized**: Responsive design with touch-friendly controls
- **Real-time Data**: Live updates with proper error handling
- **Timezone Support**: Displays times in EST/EDT
- **Collapsible Interface**: Minimize form on mobile for full-screen chart viewing

## 🏗️ Project Structure

```
src/
├── app/
│   ├── api/historical-option-bars/    # API route for Alpaca data
│   ├── globals.css                    # Global styles
│   ├── layout.tsx                     # Root layout
│   └── page.tsx                       # Main application page
├── components/
│   └── TradingViewChart.tsx           # Chart component
└── utils/
    ├── fetchHistoricalOptionBars.ts   # API client
    ├── formatAlpacaOptionSymbol.ts    # Symbol formatting
    └── transformChartData.ts          # Data transformation
```

## 🔧 Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🌐 API Integration

The application integrates with Alpaca Markets API:
- **Endpoint**: `https://data.alpaca.markets/v1beta1/options/bars`
- **Data**: Historical options bars with configurable timeframes (1Min, 5Min, 15Min, 30Min)
- **Limit**: Up to 10,000 bars per request
- **Authentication**: API Key and Secret via headers

## 🚀 Deployment

### Vercel (Recommended)

1. Connect your repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Other Platforms

The application can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 📄 License

This project is private and proprietary.

## 🤝 Contributing

This is a private project. For questions or issues, please contact the repository owner.
